/* --- General Body & Typography --- */
body {
    font-family: 'Inter', sans-serif;
    color: #333333;
    background-color: #FFFFFF;
    margin: 0;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: #005A9C;
}

h1 { font-size: 2.8rem; margin-bottom: 0.5rem; }
h2.section-title { font-size: 2.2rem; text-align: center; margin-bottom: 3rem; }
h3 { font-size: 1.5rem; }

a { color: #005A9C; text-decoration: none; }
a:hover { text-decoration: underline; }

/* --- Header --- */
.main-header {
    background: #FFFFFF;
    border-bottom: 1px solid #F5F5F5;
    padding: 8px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px; /* Further reduced gap for optimal space usage */
}

.logo {
    flex-shrink: 1; /* Allow logo to shrink if needed */
    flex-grow: 0;
    flex-basis: 25%; /* Maximum 25% of header width */
    max-width: 25%;
    overflow: hidden;
}

.logo a {
    display: block;
    text-decoration: none;
}

.logo-image {
    height: 45px;
    width: auto;
    max-width: 120px;
    object-fit: contain;
    display: block;
}

.main-nav {
    flex-grow: 1; /* Allow nav to take up available space */
    flex-shrink: 1;
    flex-basis: 50%; /* Give navigation more space */
    min-width: 0; /* Allow nav to shrink below its content size */
}

.main-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    justify-content: center; /* Center the nav items */
    flex-wrap: wrap; /* Allow nav items to wrap on smaller screens */
    gap: 5px 10px; /* Add gap for items, allows vertical spacing if wrapped */
}

.main-nav li {
    margin: 0; /* Replaced by gap property on ul */
}

.main-nav a {
    font-weight: 600;
    font-size: 0.95rem; /* Slightly reduced font size */
    color: #333333;
    padding: 4px 8px; /* Further reduced padding for compact header */
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    border-radius: 4px;
    white-space: nowrap; /* Keep nav links on one line */
}

.main-nav a:hover, .main-nav a.active {
    color: #005A9C;
    border-bottom-color: #F68D2E;
    background-color: rgba(0, 90, 156, 0.05);
}

.main-nav a:focus {
    outline: 2px solid #005A9C;
    outline-offset: 2px;
}

.header-contact {
    display: flex;
    align-items: center;
    flex-shrink: 1; /* Allow contact section to shrink if needed */
    flex-basis: 25%; /* Roughly 25% for contact section */
    justify-content: flex-end;
    min-width: 0;
}

.phone-number {
    font-weight: 700;
    font-size: 1rem; /* Optimized for compact header */
    color: #005A9C;
    margin-right: 12px; /* Further reduced margin */
    padding: 6px 8px; /* Further reduced padding */
    border-radius: 4px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.phone-number:hover {
    background-color: rgba(0, 90, 156, 0.1);
    transform: scale(1.05);
}

/* Specific styling for the header CTA button */
.header-contact .cta-button {
    font-size: 0.85rem;
    padding: 8px 14px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: 2px solid #005A9C;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    color: #005A9C;
    min-width: 44px;
    min-height: 44px;
}

.mobile-menu-toggle:focus {
    outline: 2px solid #F68D2E;
    outline-offset: 2px;
}

.hamburger-icon {
    display: block;
    width: 20px;
    height: 2px;
    background-color: #005A9C;
    position: relative;
}

.hamburger-icon::before,
.hamburger-icon::after {
    content: '';
    display: block;
    width: 20px;
    height: 2px;
    background-color: #005A9C;
    position: absolute;
    transition: all 0.3s ease;
}

.hamburger-icon::before { top: -6px; }
.hamburger-icon::after { top: 6px; }

/* --- Call to Action Buttons --- */
.cta-button {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
    color: #FFFFFF;
    background-color: #F68D2E;
    padding: 16px 32px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid #F68D2E;
    min-width: 44px;
    min-height: 44px;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.cta-button:hover {
    background-color: #e07b20;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(246, 141, 46, 0.3);
}

.cta-button:focus {
    outline: 3px solid #005A9C;
    outline-offset: 2px;
}

.cta-button:active {
    transform: translateY(0);
}

.cta-button-primary {
    background-color: #F68D2E;
    color: #FFFFFF;
    border-color: #F68D2E;
}

.cta-button-secondary {
    background-color: #FFFFFF;
    color: #005A9C;
    border: 3px solid #005A9C;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cta-button-secondary:hover {
    background-color: #005A9C;
    color: #FFFFFF;
    border-color: #005A9C;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 90, 156, 0.3);
}

.cta-button-large {
    font-size: 1.3rem;
    padding: 20px 40px;
}

.cta-button-urgent {
    background: linear-gradient(135deg, #F68D2E 0%, #e07b20 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(246, 141, 46, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(246, 141, 46, 0); }
    100% { box-shadow: 0 0 0 0 rgba(246, 141, 46, 0); }
}

/* --- Hero Section --- */
.hero {
    background: linear-gradient(135deg, #005A9C 0%, #003d6b 100%);
    color: #FFFFFF;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

/* Homepage Hero with Background Image */
.hero.homepage-hero {
    background: url('images/pro-service-homepage.jpeg') no-repeat center center/cover;
}

.hero.homepage-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 3.8rem;
    margin-bottom: 1rem;
    color: #FFFFFF;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero .tagline {
    font-size: 1.3rem;
    color: #FFFFFF;
    max-width: 700px;
    margin: 1rem auto 2.5rem;
    line-height: 1.7;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.trust-signal-hero {
    margin-top: 1.5rem;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Service Hero Specific Styling */
.service-hero {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.service-hero::before {
    background: rgba(0, 0, 0, 0.6) !important;
}

.service-hero h1,
.service-hero .tagline,
.service-hero .trust-signal-hero {
    color: #FFFFFF !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
}

.trust-signal-hero::before {
    content: '✓';
    background-color: #28A745;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

/* --- Services Overview Section --- */
.services-overview { 
    padding: 80px 0; 
    background-color: #FFFFFF;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    margin-top: 3rem;
}

.service-item {
    background: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #F68D2E;
}

.service-item h3 {
    color: #005A9C;
    margin-bottom: 1rem;
    font-size: 1.4rem;
}

.service-item p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* --- Trust Signals --- */
.trust-signals {
    background-color: #F5F5F5;
    padding: 60px 0;
}

.trust-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    text-align: center;
}

.trust-item {
    padding: 20px;
}

.trust-item .icon {
    font-size: 3rem;
    color: #F68D2E;
    margin-bottom: 1rem;
}

.trust-item h4 {
    color: #005A9C;
    margin-bottom: 0.5rem;
}

.trust-item p {
    color: #666;
    font-size: 0.95rem;
}

/* --- Customer Reviews Section --- */
.customer-reviews-preview {
    margin-bottom: 3rem;
}

.reviews-cta-container {
    margin-bottom: 3rem !important;
}

/* --- Why Choose Us CTA --- */
.why-choose-us-cta {
    background: linear-gradient(135deg, #005A9C 0%, #003d6b 100%);
    color: #FFFFFF;
    padding: 80px 0;
    text-align: center;
}

.why-choose-us-cta h2 {
    color: #FFFFFF;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.why-choose-us-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* --- Final CTA Section --- */
.cta-final {
    background: linear-gradient(135deg, #005A9C 0%, #003d6b 100%);
    color: #FFFFFF;
    padding: 80px 0;
    text-align: center;
    position: relative;
}

.cta-final::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23FFFFFF" opacity="0.1"/></svg>');
    background-size: 50px 50px;
}

.cta-final .container {
    position: relative;
    z-index: 1;
}

.cta-final h2 { 
    color: #FFFFFF; 
    font-size: 2.8rem; 
    margin-bottom: 1rem;
}

.cta-final p { 
    font-size: 1.3rem; 
    margin-bottom: 2.5rem; 
    opacity: 0.95;
}

.cta-final .cta-button {
    background-color: #F68D2E;
    color: #FFFFFF;
    font-size: 1.2rem;
    padding: 18px 36px;
}

/* --- Footer --- */
.main-footer {
    background: #333333;
    color: #F5F5F5;
    padding: 60px 0 30px;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-col h4 {
    color: #FFFFFF;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-col ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-col li {
    margin-bottom: 12px;
}

.footer-col a {
    color: #F5F5F5;
    transition: color 0.3s ease;
    padding: 4px 0;
    display: inline-block;
}

.footer-col a:hover {
    color: #F68D2E;
}

.footer-col a:focus {
    outline: 2px solid #F68D2E;
    outline-offset: 2px;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid #555;
    padding-top: 30px;
    font-size: 0.9rem;
    color: #CCC;
}

/* --- Form Styles --- */
.form-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    margin-top: 2rem;
}

.form-container-main {
    background: #FFFFFF;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #E0E0E0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333333;
    font-size: 1rem;
}

.required {
    color: #e74c3c;
    font-weight: bold;
}

.form-help {
    display: block;
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.25rem;
    font-style: italic;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #E0E0E0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    box-sizing: border-box;
    background-color: #FFFFFF;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #005A9C;
    box-shadow: 0 0 0 3px rgba(0, 90, 156, 0.1);
    background-color: #FAFAFA;
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #005A9C;
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #e74c3c;
    background-color: #fdf2f2;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: #28A745;
}

.error-message {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.error-message::before {
    content: '⚠';
    font-size: 1rem;
}

.success-message {
    background: linear-gradient(135deg, #28A745 0%, #20c997 100%);
    color: #FFFFFF;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);
}

.success-message h3 {
    color: #FFFFFF;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.form-submit {
    text-align: center;
    margin-top: 2.5rem;
    padding-top: 2rem;
    border-top: 1px solid #E0E0E0;
}

/* --- Service Page Layouts --- */
.service-page-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    margin-top: 2rem;
}

.service-details h2 {
    color: #005A9C;
    margin-bottom: 1.5rem;
}

.service-details h3 {
    color: #005A9C;
    margin: 2rem 0 1rem 0;
}

.symptom-list {
    list-style: none;
    padding: 0;
}

.symptom-list li {
    padding: 8px 0;
    border-bottom: 1px solid #E0E0E0;
    position: relative;
    padding-left: 25px;
}

.symptom-list li::before {
    content: '•';
    color: #F68D2E;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.feature-grid-small {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 2rem 0;
}

.feature-item-small {
    background: #F5F5F5;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #F68D2E;
}

.feature-item-small h4 {
    color: #005A9C;
    margin-bottom: 0.5rem;
}

.service-sidebar {
    background: #F5F5F5;
    padding: 30px;
    border-radius: 12px;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-widget {
    margin-bottom: 2rem;
}

.sidebar-widget:last-child {
    margin-bottom: 0;
}

.sidebar-widget h4 {
    color: #005A9C;
    margin-bottom: 1rem;
}

.sidebar-cta {
    background: linear-gradient(135deg, #005A9C 0%, #003d6b 100%);
    color: #FFFFFF;
    text-align: center;
    padding: 30px;
    border-radius: 12px;
}

.sidebar-cta h4 {
    color: #FFFFFF;
}

.phone-link-prominent {
    font-size: 1.4rem;
    font-weight: 700;
    color: #F68D2E;
    display: block;
    margin: 1rem 0;
    padding: 12px;
    background: rgba(246, 141, 46, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.phone-link-prominent:hover {
    background: rgba(246, 141, 46, 0.2);
    transform: scale(1.05);
}

/* --- Mobile Floating Call Button --- */
.mobile-call-button {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
    display: none; /* Hidden by default, shown only on mobile */
    width: 85%;
    max-width: 320px;
}

.mobile-call-button a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: #005A9C;
    color: #FFFFFF;
    padding: 16px 20px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1rem;
    box-shadow: 0 4px 16px rgba(0, 90, 156, 0.4);
    transition: all 0.3s ease;
    white-space: nowrap;
    width: 100%;
    text-align: center;
}

.mobile-call-button a:hover {
    background: #004080;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 90, 156, 0.5);
    color: #FFFFFF;
    text-decoration: none;
}

.mobile-call-button .call-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.mobile-call-button .call-text {
    font-size: 0.95rem;
    font-weight: 700;
}

/* --- Responsive Design --- */
@media (max-width: 992px) {
    .main-header .container {
        flex-wrap: nowrap;
    }

    .main-nav, .header-contact .cta-button {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
        order: 3;
    }

    .logo {
        order: 1;
        flex: 1;
    }

    .header-contact {
        order: 2;
        flex-shrink: 0;
    }

    .main-nav.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        background: #FFF;
        width: 100%;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        padding: 20px;
        z-index: 999;
        border-top: 1px solid #E0E0E0;
    }

    .main-nav.active li {
        margin: 10px 0;
    }

    .main-nav.active a {
        padding: 12px 0;
        font-size: 1.1rem;
        border-bottom: 1px solid #F5F5F5;
    }

    .why-choose-us-cta .container {
        flex-direction: column;
    }

    .service-page-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .form-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .service-sidebar {
        position: static;
    }

    .hero {
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2.8rem;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.2rem;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .header-contact .phone-number {
        display: none;
    }

    /* Show mobile floating call button on mobile devices */
    .mobile-call-button {
        display: block;
    }

    .cta-button {
        font-size: 1rem;
        padding: 14px 24px;
    }

    .cta-button-large {
        font-size: 1.1rem;
        padding: 16px 28px;
    }

    .service-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .trust-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-grid-small {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero .tagline {
        font-size: 1.1rem;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
    }

    .main-header {
        padding: 10px 0;
    }

    .logo-image {
        height: 50px; /* Larger on mobile since no text */
        max-width: 150px; /* Allow more space on mobile */
    }

    /* Mobile call button adjustments for smaller screens */
    .mobile-call-button {
        width: 90%;
        max-width: 280px;
        bottom: 15px;
    }

    .mobile-call-button a {
        padding: 14px 18px;
        font-size: 0.95rem;
    }

    .mobile-call-button .call-text {
        font-size: 0.9rem;
    }
}

/* --- Accessibility Enhancements --- */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #005A9C;
    color: #FFFFFF;
    padding: 8px;
    text-decoration: none;
    z-index: 9999;
    border-radius: 4px;
}

.skip-link:focus {
    top: 6px;
}

/* --- Performance Optimizations --- */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-load.loaded {
    opacity: 1;
}

/* --- Print Styles --- */
@media print {
    .main-header,
    .main-footer,
    .cta-button,
    .mobile-menu-toggle {
        display: none;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3 {
        page-break-after: avoid;
    }
}
